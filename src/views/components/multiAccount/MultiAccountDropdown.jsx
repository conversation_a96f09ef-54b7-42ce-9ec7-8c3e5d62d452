import React, { useState, useEffect, useRef } from "react";
import {
  Box,
  Typography,
  CircularProgress,
  IconButton,
  Button,
  Popover,
} from "@mui/material";
import DeleteIcon from "@mui/icons-material/Delete";
import AddIcon from "@mui/icons-material/Add";
import { useMultiAccount } from "../../../helpers/context/MultiAccountContext";
import { fetchFromStorage } from "../../../helpers/context/storage";
import siteConstant from "../../../helpers/constant/siteConstant";

const MultiAccountDropdown = ({ onSelect, onAddAccount }) => {
  const {
    selectedAccount,
    storedAccounts,
    handleAccountSelect,
    removeStoredAccount,
    loadingAccounts,
    switchingAccount,
  } = useMultiAccount();

  const [isVisible, setIsVisible] = useState(false);
  const [accountToDelete, setAccountToDelete] = useState(null);
  const [popoverAnchorEl, setPopoverAnchorEl] = useState(null);

  const observer = useRef(null);
  const dropdownRef = useRef(null);

  // Get current login user ID
  const currentUserData = fetchFromStorage(
    siteConstant?.INDENTIFIERS?.USERDATA
  );
  const loginUserId = currentUserData?.userId;

  // Initialize Intersection Observer
  useEffect(() => {
    observer.current = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting) {
          setIsVisible(true);
          observer.current.disconnect();
        }
      },
      { threshold: 0.1 }
    );

    if (dropdownRef.current) {
      observer.current.observe(dropdownRef.current);
    }

    return () => {
      if (observer.current) {
        observer.current.disconnect();
      }
    };
  }, []);

  const handleAccountClick = async (account) => {
    await handleAccountSelect(account);
    onSelect?.(account);
  };

  const handleDeleteClick = (event, account) => {
    event.stopPropagation();
    setAccountToDelete(account);
    setPopoverAnchorEl(event.currentTarget);
  };

  const handleDeleteCancel = () => {
    setPopoverAnchorEl(null);
    setAccountToDelete(null);
  };

  const handleDeleteConfirm = () => {
    if (accountToDelete) {
      removeStoredAccount(accountToDelete.userId);
    }
    setPopoverAnchorEl(null);
    setAccountToDelete(null);
  };

  const isMainAccount = (account) => {
    return account.isMainAccount === true;
  };

  if (storedAccounts.length === 0 && !loadingAccounts) {
    return (
      <ul className="p-2 max-h-[300px] overflow-y-auto w-full sm:min-w-[300px] max-w-[100%]">
        <li className="flex items-center justify-center p-4">
          <CircularProgress size={24} />
          <Typography variant="body2" sx={{ ml: 2 }}>
            Loading accounts...
          </Typography>
        </li>
      </ul>
    );
  }

  return (
    <>
      <ul
        ref={dropdownRef}
        className="p-2  overflow-y-auto w-full sm:min-w-[300px] max-w-[100%] font-Ubuntu"
      >
        {loadingAccounts ? (
          <li className="flex items-center justify-center p-4">
            <CircularProgress size={24} />
            <Typography variant="body2" sx={{ ml: 2 }}>
              Loading accounts...
            </Typography>
          </li>
        ) : storedAccounts.length > 0 ? (
          storedAccounts.map((account, index) => (
            <li
              key={account.userId || index}
              className={`bg-white border-gray-200 p-3 flex items-start sm:items-center gap-4 flex-wrap border-b hover:bg-gray-50 transition cursor-pointer w-full
                ${
                  selectedAccount?.userId === account.userId
                    ? "border-[#563D39]"
                    : "border-gray-200"
                }
                ${index === 0 ? "rounded-t-lg" : ""}
                ${index === storedAccounts.length - 1 ? "rounded-b-lg" : ""}`}
              onClick={() => handleAccountClick(account)}
              disabled={switchingAccount}
            >
              <div className="relative">
                <img
                  src={
                    account.profileImage ||
                    siteConstant.SOCIAL_ICONS.DUMMY_PROFILE
                  }
                  alt="Profile"
                  className="h-10 w-10 min-w-[2.5rem] rounded-full border object-cover"
                  onError={(e) => {
                    e.target.src = siteConstant.SOCIAL_ICONS.DUMMY_PROFILE;
                  }}
                />
                {selectedAccount?.userId === account.userId && (
                  <div className="absolute -top-1 -right-1 w-4 h-4 bg-[#563D39] rounded-full flex items-center justify-center">
                    <div className="w-2 h-2 bg-white rounded-full"></div>
                  </div>
                )}
                {switchingAccount &&
                  selectedAccount?.userId === account.userId && (
                    <div className="absolute inset-0 bg-white bg-opacity-75 rounded-full flex items-center justify-center">
                      <CircularProgress size={16} />
                    </div>
                  )}
              </div>

              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2">
                  <h3 className="text-base font-medium text-gray-900 truncate">
                    {account.name}
                  </h3>
                  {isMainAccount(account) && (
                    <span className="px-2 py-1 text-xs bg-[#563D39] text-white rounded-full">
                      Main
                    </span>
                  )}
                </div>
                <p className="text-sm text-gray-500 mb-1 truncate">
                  @{account.username}
                </p>
              </div>

              {!isMainAccount(account) && (
                <IconButton
                  size="small"
                  onClick={(e) => handleDeleteClick(e, account)}
                  sx={{
                    color: "#ef4444",
                    "&:hover": {
                      backgroundColor: "#fef2f2",
                    },
                  }}
                >
                  <DeleteIcon fontSize="small" />
                </IconButton>
              )}
            </li>
          ))
        ) : (
          <li className="flex items-center justify-center p-4 text-gray-500">
            <Typography variant="body2">No accounts found</Typography>
          </li>
        )}

        {/* Add Account Button */}
        <li className="mt-2">
          <button
            onClick={onAddAccount}
            className="w-full p-3 flex items-center gap-3 text-[#563D39] hover:bg-gray-50 rounded-lg border border-dashed border-gray-300 transition-colors"
          >
            <AddIcon fontSize="small" />
            <span className="text-sm font-medium">Add Other Account</span>
          </button>
        </li>
      </ul>

      {/* Styled Popover for Delete Confirmation */}
      <Popover
        className="ml-[-10px] mt-[13px]"
        id={
          Boolean(popoverAnchorEl) ? "delete-confirmation-popover" : undefined
        }
        open={Boolean(popoverAnchorEl)}
        anchorEl={popoverAnchorEl}
        onClose={handleDeleteCancel}
        anchorOrigin={{
          vertical: "bottom",
          horizontal: "right",
        }}
        transformOrigin={{
          vertical: "top",
          horizontal: "right",
        }}
        PaperProps={{
          sx: {
            mt: 1,
            borderRadius: 2,
            minWidth: 300,
            maxWidth: 400,
            boxShadow: "1 8px 32px rgba(80, 80, 80, 0.10)",
            p: 2,
            background: "#fff",
            transition: "all 0.2s cubic-bezier(0.4,0,0.2,1)",
          },
        }}
        PopperProps={{
          modifiers: [
            {
              name: "offset",
              options: {
                offset: [-20, 10],
              },
            },
          ],
        }}
      >
        <Box>
          <Typography
            variant="h6"
            gutterBottom
            sx={{ fontWeight: 600, fontSize: "1.1rem", textAlign: "center" }}
          >
            Remove Account
          </Typography>
          <Typography
            variant="body2"
            sx={{ color: "#666", lineHeight: 1.5 }}
            textAlign="center"
          >
            This will remove the account from this User. You'll have to sign in
            again to access it later.
            <br />
            <br />
            Would you like to continue?
          </Typography>
          <Box mt={3} display="flex" justifyContent="center" gap={1}>
            <Button
              onClick={handleDeleteCancel}
              sx={{
                color: "#666",
                textTransform: "none",
                "&:hover": {
                  backgroundColor: "#f5f5f5",
                },
              }}
            >
              Cancel
            </Button>
            <Button
              onClick={handleDeleteConfirm}
              variant="contained"
              sx={{
                backgroundColor: "#ef4444",
                textTransform: "none",
                "&:hover": {
                  backgroundColor: "#dc2626",
                },
              }}
            >
              Remove
            </Button>
          </Box>
        </Box>
      </Popover>
    </>
  );
};

export default MultiAccountDropdown;
