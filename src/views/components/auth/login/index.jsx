import React, { useState, useContext } from "react";
import { Visibility, VisibilityOff } from "@mui/icons-material";
import { Input } from "@material-tailwind/react";

import { ErrorMessage, Field, Formik } from "formik";
import * as Yup from "yup";
import { useNavigate, Link } from "react-router-dom";
import {
  fetchFromStorage,
  saveToStorage,
} from "../../../../helpers/context/storage";
import login from "../../../../assets/images/Login/sign-in.svg";
import X from "../../../../assets/images/Login/X.svg";
import facebook_Bottom from "../../../../assets/images/Login/Facebook_Bottom.svg";
import facebookk from "../../../../assets/images/Login/Facebookk.svg";
import Instagram_Top from "../../../../assets/images/Login/Instagram_Top.svg";
import Instagram from "../../../../assets/images/Login/Instagram.svg";
import Pinterest_Right from "../../../../assets/images/Login/Pinterest_Right.svg";
import Pinterest from "../../../../assets/images/Login/Pinterest.svg";
import Tiktok from "../../../../assets/images/Login/Tiktok.svg";
import X_Right from "../../../../assets/images/Login/X_Right.svg";
import Youtube from "../../../../assets/images/Login/Youtube.svg";
import Email from "../../../../assets/images/Login/Email.svg";
import Lock from "../../../../assets/images/Login/Lock.svg";
import ShowPass from "../../../../assets/images/ForgotPass/SeePass.svg";
import HidePass from "../../../../assets/images/ForgotPass/HidePass.svg";
import { CustomTextField } from "../../custom/CustomTextField";
import { URL } from "../../../../helpers/constant/Url";
import { setApiMessage } from "../../../../helpers/context/toaster";
import siteConstant from "../../../../helpers/constant/siteConstant";
import DialogueModel from "../../../admin/common/dialogue";
import { IntlContext } from "../../../../App";

import LoadingSpinner from "../../../../helpers/UI/LoadingSpinner";
import apiInstance from "../../../../helpers/Axios/axiosINstance";
import { useMultiAccount } from "../../../../helpers/context/MultiAccountContext";

const LoginPage = () => {
  const [open, setopen] = useState(false);
  const intlContext = useContext(IntlContext);
  const localesData = intlContext?.messages;
  const navigate = useNavigate();
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const { addStoredAccount } = useMultiAccount();

  const toggleShowPassword = () => {
    setShowPassword(!showPassword);
  };

  const inputWrapStyle = "relative mb-5 mt-5";
  const legendStyle =
    "absolute text-sm text-[#1C1B1F] left-3 top-[2px] bg-white px-1 z-10 mb-4";
  const inputStyle =
    "w-full  pl-14  py-4 mt-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#614036] focus:border-[#614036] focus:outline-none";

  return (
    <div className="h-screen overflow-y-auto">
      <div className="relative min-h-screen bg-white flex flex-col items-center px-4 font-Ubuntu max-w-[90%] mx-auto ">
        <div className="logo  absolute top-10 md:top-[80px] lg:top-[100px] xl:top-[170px] md:left-[137px] left-6 right-0  sm:h-36 sm:w-36  h-[115px] w-[115px] sm:mt-4 mt-2 z-50">
          <img src={siteConstant.SOCIAL_ICONS.FLOWKARLOGO} alt="logo" />
        </div>
        {/* Background icons in a single container, z-0, pointer-events-none */}
        <div className="pointer-events-none absolute inset-0 z-0 ">
          <div className="absolute top-0 left-40 ">
            <img src={X} alt="X Logo" className="max-w-full h-auto" />
          </div>
          <div className="absolute top-[25%] left-0 ">
            <img
              src={facebookk}
              alt="Facebook Logo"
              className="max-w-full h-auto"
            />
          </div>
          <div className="absolute bottom-16 left-[15%] ">
            <img
              src={Instagram}
              alt="Instagram Logo"
              className="max-w-full h-auto"
            />
          </div>
          <div className="absolute bottom-0 left-[50%] ">
            <img
              src={facebook_Bottom}
              alt="Facebook Bottom Logo"
              className="max-w-full h-auto"
            />
          </div>
          <div className="absolute bottom-[10%] right-[30%] ">
            <img
              src={X_Right}
              alt="X Right Logo"
              className="max-w-full h-auto"
            />
          </div>
          <div className="absolute bottom-[5%] right-[10%] ">
            <img
              src={Pinterest_Right}
              alt="Pinterest Right Logo"
              className="max-w-full h-auto"
            />
          </div>
          <div className="absolute bottom-[50%] right-[2%] ">
            <img
              src={Youtube}
              alt="Youtube Logo"
              className="max-w-full h-auto"
            />
          </div>
          <div className="absolute top-[5%] right-[5%] ">
            <img
              src={Instagram_Top}
              alt="Instagram Top Logo"
              className="max-w-full h-auto"
            />
          </div>
          <div className="absolute top-[10%] right-[30%] ">
            <img src={X_Right} alt="X Top Logo" className="max-w-full h-auto" />
          </div>
          <div className="absolute top-[4%] right-[50%] ">
            <img src={Tiktok} alt="Tiktok Logo" className="max-w-full h-auto" />
          </div>
          <div className="absolute top-[25%] right-[50%] ">
            <img
              src={Pinterest}
              alt="Pinterest Logo"
              className="max-w-full h-auto"
            />
          </div>
          {/* Show only a few icons on mobile for less clutter */}
          <div className="absolute top-2 left-2 ">
            <img src={X} alt="X Logo" className="w-10 h-10" />
          </div>
          <div className="absolute bottom-2 right-2 ">
            <img src={Instagram} alt="Instagram Logo" className="w-10 h-10" />
          </div>
        </div>
        {/* Form and illustration container, z-10, relative */}
        <div className="relative z-10 w-full flex flex-col justify-center items-center md:flex-row md:-ml-7 mt-28 sm:mt-28 md:mt-36 lg:mt-[165px]  xl:mt-56">
          <div className="w-full md:w-[50%] max-w-md mx-auto md:ml-[133px] bg-white bg-opacity-90 rounded-xl p-4 md:p-0 shadow md:shadow-none">
            {/* <h1 className="text-4xl font-bold text-[#614036] mb-2">Flowkar</h1> */}
            {/* <img
            src={siteConstant.SOCIAL_ICONS.FLOWKARLOGO}
            alt="logo"
            className="sm:h-36 sm:w-36  h-28 w-28 sm:mt-4 mt-2"
          /> */}
            <h2 className="text-3xl text-[#614036] mb-2 z-50">
              <span className="font-bold">Sign</span> In
            </h2>

            <p className="text-[#AA8882] mb-8 z-50">
              Access your account and continue exploring
            </p>

            <Formik
              initialValues={{
                email: "",
                password: "",
              }}
              validationSchema={Yup.object().shape({
                email: Yup.string()
                  .email(
                    localesData?.USER_WEB?.MESSAGES?.INVALID_EMAIL_ADDRESS ||
                      "Invalid email address"
                  )
                  .required(
                    localesData?.USER_WEB?.MESSAGES?.EMAIL_REQUIRED ||
                      "Email is required"
                  ),
                password: Yup.string().required(
                  localesData?.USER_WEB?.MESSAGES?.PASSWORD_REQUIRED ||
                    "Password is required"
                ),
              })}
              onSubmit={async (
                requestData,
                { setSubmitting, setFieldError }
              ) => {
                setLoading(true);
                try {
                  const form = new FormData();
                  form.append("creds", requestData?.email);
                  form.append("password", requestData?.password);
                  const { status, data } = await apiInstance.post(
                    URL.LOGIN,
                    form
                  );

                  if (data?.status && data?.token) {
                    setApiMessage("success", data?.message);
                    delete data?.message;
                    delete data?.status;
                    saveToStorage(siteConstant?.INDENTIFIERS?.USERDATA, data);

                    // Initialize multi-account system with current user
                    // Use brand ID from API response, fallback to stored brand or default
                    const brandId =
                      data.brand_id ||
                      data.brandId ||
                      parseInt(localStorage.getItem("BrandId"), 10) ||
                      1;

                    const currentAccount = {
                      userId: data.user_id || data.id,
                      name: data.name,
                      username: data.username,
                      profileImage: data.profile_image || "",
                      token: data.token,
                      email: requestData?.email,
                      brandId: brandId,
                      isMainAccount: true,
                    };
                    addStoredAccount(currentAccount);

                    // ✅ SET PERMISSIONS IMMEDIATELY AFTER LOGIN SUCCESS
                    // Main account gets all permissions [1,2,3,4,5,6,7]
                    const allPermissions = [1, 2, 3, 4, 5, 6, 7];
                    const allPermissionsFormatted = {
                      Post: true,
                      Message: true,
                      Analytics: true,
                      User_Management: true,
                      Brand_Management: true,
                      Block_UnBlock: true,
                      FeedBack: true,
                    };

                    // Store permissions immediately
                    localStorage.setItem(
                      "userPermissions",
                      JSON.stringify(allPermissions)
                    );
                    localStorage.setItem(
                      "userPermissionsFormatted",
                      JSON.stringify(allPermissionsFormatted)
                    );

                    navigate("/dashboard");
                    // if (userData?.is_admin) {
                    //   setopen(true);
                    // } else {
                    //   navigate("/dashboard");
                    // }
                  } else {
                    // Set field-specific errors if available
                    if (data?.message?.toLowerCase().includes("password")) {
                      setFieldError("password", data.message);
                    } else if (
                      data?.message?.toLowerCase().includes("email") ||
                      data?.message?.toLowerCase().includes("account")
                    ) {
                      setFieldError("email", data.message);
                    } else {
                      setApiMessage("error", data?.message);
                    }
                  }
                } catch (error) {
                  // Handle different error types
                  if (error?.response?.status === 401) {
                    setFieldError("password", "Invalid email or password");
                    setApiMessage("error", "Invalid email or password");
                  } else if (error?.response?.status === 404) {
                    setFieldError("email", "Account not found");
                    setApiMessage("error", "Account not found");
                  } else {
                    setFieldError(
                      "password",
                      error?.message || "An error occurred"
                    );
                    setApiMessage(
                      "error",
                      error?.message || "An error occurred"
                    );
                  }
                } finally {
                  setLoading(false);
                  setSubmitting(false);
                }
              }}
              validateOnBlur={false}
              validateOnChange={false}
            >
              {({
                errors,
                handleBlur,
                handleChange,
                handleSubmit,
                touched,
                values,
                isSubmitting,
              }) => (
                <form onSubmit={handleSubmit}>
                  {/* Email Input Field */}
                  <div className="mb-6 relative">
                    {" "}
                    {/* Increased bottom margin */}
                    <span className="absolute top-[34%] -translate-y-1/2 left-4 flex items-center pointer-events-none z-10">
                      <img src={Email} alt="Email" className="w-5 h-5" />
                    </span>
                    <Field
                      as={CustomTextField}
                      variant="outlined"
                      borderRadius="12px"
                      label="Email"
                      fullWidth
                      type="text"
                      id="email"
                      name="email"
                      placeholder=""
                      value={values.email}
                      onChange={handleChange}
                      onBlur={handleBlur}
                      error={touched.email && errors.email}
                      sx={{
                        "& .MuiOutlinedInput-root": {
                          paddingLeft: "45px",
                          "& fieldset": {
                            borderRadius: "20px",
                            borderColor:
                              touched.email && errors.email
                                ? "#f44336"
                                : undefined,
                          },
                          "&:hover fieldset": {
                            borderColor:
                              touched.email && errors.email
                                ? "#f44336"
                                : undefined,
                          },
                          "&.Mui-focused fieldset": {
                            borderColor:
                              touched.email && errors.email
                                ? "#f44336"
                                : "#614036",
                          },
                        },
                        "& .MuiOutlinedInput-input": {
                          paddingLeft: "10px !important",
                          color: "#000000 !important",
                          fontWeight: "normal !important",
                          "&:-webkit-autofill": {
                            WebkitTextFillColor: "#000000 !important",
                            WebkitBoxShadow:
                              "0 0 0 1000px white inset !important",
                            fontWeight: "normal !important",
                          },
                        },
                        "& .MuiInputLabel-root": {
                          left: "35px",
                          transform: "translate(14px, 16px) scale(1)",
                          color: "#000000  !important",
                          "&.MuiInputLabel-shrink": {
                            transform: "translate(5px, -9px) scale(0.75)",
                          },
                          "&.Mui-focused": {
                            color: "#000000  !important",
                          },
                        },
                      }}
                    />
                    {/* Error Message - now always takes up space but conditionally visible */}
                    <div
                      className={`h-5 mt-1 ${
                        touched.email && errors.email ? "visible" : "invisible"
                      }`}
                    >
                      <p className="text-red-500 text-sm ml-1">
                        {errors.email}
                      </p>
                    </div>
                  </div>

                  {/* Password Input Field */}
                  <div className="mb-6 relative">
                    {" "}
                    {/* Increased bottom margin */}
                    <span className="absolute top-[34%] -translate-y-1/2 left-4 flex items-center pointer-events-none z-10">
                      <img src={Lock} alt="Lock" className="w-5 h-5" />
                    </span>
                    <Field
                      as={CustomTextField}
                      variant="outlined"
                      borderRadius="12px"
                      label="Password"
                      fullWidth
                      type={showPassword ? "text" : "password"}
                      id="password"
                      name="password"
                      placeholder=""
                      value={values.password}
                      onChange={handleChange}
                      onBlur={handleBlur}
                      error={touched.password && errors.password}
                      sx={{
                        "& .MuiOutlinedInput-root": {
                          paddingLeft: "45px",
                          paddingRight: "45px",
                          "& fieldset": {
                            borderRadius: "20px",
                            borderColor:
                              touched.password && errors.password
                                ? "#f44336"
                                : undefined,
                          },
                          "&:hover fieldset": {
                            borderColor:
                              touched.password && errors.password
                                ? "#f44336"
                                : undefined,
                          },
                          "&.Mui-focused fieldset": {
                            borderColor:
                              touched.password && errors.password
                                ? "#f44336"
                                : "#614036",
                          },
                        },
                        "& .MuiOutlinedInput-input": {
                          paddingLeft: "10px !important",
                          paddingRight: "10px !important",
                          color: "#000000 !important",
                          fontWeight: "normal !important",
                          "&:-webkit-autofill": {
                            WebkitTextFillColor: "#000000 !important",
                            WebkitBoxShadow:
                              "0 0 0 1000px white inset !important",
                            fontWeight: "normal !important",
                          },
                        },
                        "& .MuiInputLabel-root": {
                          left: "35px",
                          transform: "translate(14px, 16px) scale(1)",
                          color: "#000000  !important",
                          "&.MuiInputLabel-shrink": {
                            transform: "translate(5px, -9px) scale(0.75)",
                          },
                          "&.Mui-focused": {
                            color: "#000000  !important",
                          },
                        },
                      }}
                    />
                    <span className="absolute top-[34%] -translate-y-1/2 right-4 flex items-center z-50">
                      <button
                        type="button"
                        className="flex items-center"
                        onClick={() => setShowPassword(!showPassword)}
                      >
                        {showPassword ? (
                          <img
                            src={ShowPass}
                            alt=""
                            className="h-[20px] w-[20px]"
                          />
                        ) : (
                          <img
                            src={HidePass}
                            alt=""
                            className="h-[20px] w-[20px]"
                          />
                        )}
                      </button>
                    </span>
                    {/* Error Message - now always takes up space but conditionally visible */}
                    <div
                      className={`h-5 mt-1 ${
                        touched.password && errors.password
                          ? "visible"
                          : "invisible"
                      }`}
                    >
                      <p className="text-red-500 text-sm ml-1">
                        {errors.password}
                      </p>
                    </div>
                  </div>

                  <div className="text-right mb-6">
                    <button
                      type="button"
                      className="text-sm text-[#614036] hover:underline"
                      onClick={() => navigate("/forgot-password")}
                    >
                      Forgotten Password?
                    </button>
                  </div>

                  <div className="flex justify-center items-center">
                    <button
                      type="submit"
                      className={`w-[50%] py-3 rounded-[12px] font-medium transition duration-300 ${
                        loading
                          ? "bg-gray-400 cursor-not-allowed"
                          : "bg-[#614036] hover:bg-[#7a5046] text-white"
                      }`}
                      disabled={loading}
                    >
                      {loading ? (
                        <LoadingSpinner
                          text="Loading..."
                          spinnerSize="h-4 w-4"
                          textColor="text-white"
                        />
                      ) : (
                        <span>
                          {localesData?.USER_WEB?.SIGN_IN || "Sign In"}
                        </span>
                      )}
                    </button>
                  </div>

                  <div className="text-center mt-6">
                    <p className="text-gray-700">
                      Don't have an account?{" "}
                      <Link
                        to="/sign-up"
                        className="text-[#614036] font-medium hover:underline"
                      >
                        Sign up
                      </Link>
                    </p>
                  </div>
                </form>
              )}
            </Formik>
          </div>

          <div className=" w-[50%]">
            <div className="flex justify-center items-center h-full">
              <img
                src={login}
                alt="Login illustration"
                className="max-w-full h-auto hidden md:block"
              />
            </div>
          </div>
        </div>
        <DialogueModel open={open} />
      </div>
    </div>
  );
};

export default LoginPage;
